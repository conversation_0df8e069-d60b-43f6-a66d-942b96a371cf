data "incident_user" "jesse" {
  email = "<EMAIL>"
}

data "incident_user" "na" {
  email = "<EMAIL>"
}

data "incident_user" "sam" {
  email = "<EMAIL>"
}

data "incident_user" "juandiego" {
  email = "<EMAIL>"
}

module "realtime_schedule" {
  source = "./modules/incident-schedule"

  team_name = "Realtime"
  schedule_versions = [
    {
      effective_from = "2025-04-21T00:00:00Z"
      users = [
        data.incident_user.sam.id,
        data.incident_user.na.id,
        data.incident_user.jesse.id,
      ]
    }
  ]
}

module "realtime_escalation_policy" {
  source = "./modules/incident-escalation-policy"

  team_name          = "Realtime"
  schedule_id        = module.realtime_schedule.schedule_id
  escalation_user_id = data.incident_user.juandiego.id
  slack_channel_id   = "C03UNLWQGRZ" # team-realtime-alert-prod
}

module "realtime_team" {
  source = "./modules/incident-team"

  team_name            = "Realtime"
  escalation_policy_id = module.realtime_escalation_policy.escalation_policy_id
}

# Moved blocks to prevent resource changes
moved {
  from = module.realtime_team.incident_schedule.team_schedule[0]
  to   = module.realtime_schedule.incident_schedule.team_schedule
}

moved {
  from = module.realtime_team.incident_escalation_path.team_escalation[0]
  to   = module.realtime_escalation_policy.incident_escalation_path.team_escalation
}
