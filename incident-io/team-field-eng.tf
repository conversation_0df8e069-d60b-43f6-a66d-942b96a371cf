data "incident_user" "nathaniel" {
  email = "<EMAIL>"
}

data "incident_user" "noah" {
  email = "<EMAIL>"
}

data "incident_user" "leo" {
  email = "<EMAIL>"
}

locals {
  field_eng_team_name = "Field Eng"
}

module "field_eng_team" {
  source               = "./modules/incident-team"
  team_name            = local.field_eng_team_name
  escalation_policy_id = incident_escalation_path.field_eng_team_escalation.id
}

module "field_eng_schedule" {
  source = "./modules/incident-schedule"

  team_name = local.field_eng_team_name
  schedule_versions = [
    {
      effective_from = "2025-04-21T00:00:00Z"
      users = [
        data.incident_user.noah.id,
        data.incident_user.nathaniel.id,
      ]
    },
    {
      effective_from = "2025-06-02T00:00:00Z"
      users = [
        data.incident_user.leo.id,
        data.incident_user.noah.id,
        data.incident_user.nathaniel.id,
      ]
    }
  ]
}

/**
 * Field Eng Escalation Path
 * Page for P1 alerts during all hours.
 * All other alerts (P2 and below) sent to slack.
 */
resource "incident_escalation_path" "field_eng_team_escalation" {
  name = "${local.field_eng_team_name} Team Escalation"

  lifecycle {
    ignore_changes = [
      # To avoid circular references, link teams to escalation paths on the team resource.
      team_ids
    ]
  }

  path = [
    {
      id   = local.start_node_id
      type = "if_else"
      if_else = {
        # Defined by working_hours property
        conditions = local.is_p1_alert_condition
        then_path  = local.paging_escalation
        else_path  = local.slack_alert
      }
    }
  ]
}

###### Helpers #######

locals {
  # A random string that must be unique within the escalation path
  start_node_id       = "start"
  time_to_ack_seconds = 900
  schedule_id         = module.field_eng_schedule.schedule_id
  slack_channel_id    = "C08HX98JFCN" # team-field-eng-alert-prod
  # Progressively escalate if unacknowledged:
  # 1. Page the on-call person
  # 2. Page the entire team
  # 3. Page the escalation user (oftentimes the manager)
  paging_escalation = [
    {
      type = "level"
      level = {
        targets = [
          {
            type          = "schedule"
            id            = local.schedule_id
            urgency       = "high"
            schedule_mode = "currently_on_call"
          }
        ]
        time_to_ack_seconds = local.time_to_ack_seconds
      }
    },
    {
      type = "level"
      level = {
        targets = [
          {
            type          = "schedule"
            id            = local.schedule_id
            urgency       = "high"
            schedule_mode = "all_users"
          }
        ]
        time_to_ack_seconds = local.time_to_ack_seconds
      }
    },
    {
      type = "level"
      level = {
        targets = [
          {
            type    = "user"
            id      = data.incident_user.dorian.id
            urgency = "high"
          }
        ]
        time_to_ack_seconds = local.time_to_ack_seconds
      }
    },
    {
      type = "repeat"
      repeat = {
        repeat_times = 1
        to_node      = local.start_node_id
      }
    }
  ]

  slack_alert = [{
    # Need to explicitly define an `id` field for the first node in the escalation tree
    id   = "slack-alert"
    type = "notify_channel"
    notify_channel = {
      targets = [
        {
          type    = "slack_channel"
          id      = local.slack_channel_id
          urgency = "high"
        }
      ]
      time_to_ack_seconds = 0
    }
  }]

  is_p1_alert_condition = [
    {
      operation = "one_of"
      subject   = "escalation.priority"
      param_bindings = [
        {
          array_value = [
            {
              literal = data.incident_catalog_entry.alert_priority_p1.id
            },
          ]
        },
      ]
    },
  ]
}

data "incident_catalog_type" "alert_priority" {
  name = "Alert Priority"
}

data "incident_catalog_entry" "alert_priority_p1" {
  catalog_type_id = data.incident_catalog_type.alert_priority.id
  identifier      = "01JRE0MRTE4NJF3HSSPTN13K6V"
}

moved {
  from = incident_schedule.field_eng_team_schedule
  to   = module.field_eng_schedule.incident_schedule.team_schedule
}
