
module "forecasting_schedule" {
  source = "./modules/incident-schedule"

  team_name = "Forecasting"
  schedule_versions = [
    {
      effective_from = "2025-04-21T00:00:00Z"
      users = [
        data.incident_user.sean.id,
        data.incident_user.alex.id
      ]
    }
  ]
}

module "forecasting_escalation_policy" {
  source = "./modules/incident-escalation-policy"

  team_name          = "Forecasting"
  schedule_id        = module.forecasting_schedule.schedule_id
  escalation_user_id = data.incident_user.michael.id
  slack_channel_id   = "C0882PR2XB7" # bot-ops-forecasting-prod
}

module "forecasting_team" {
  source = "./modules/incident-team"

  team_name            = "Forecasting"
  escalation_policy_id = module.forecasting_escalation_policy.escalation_policy_id
}

# Moved blocks to prevent resource changes
moved {
  from = module.forecasting_team.incident_schedule.team_schedule[0]
  to   = module.forecasting_schedule.incident_schedule.team_schedule
}

moved {
  from = module.forecasting_team.incident_escalation_path.team_escalation[0]
  to   = module.forecasting_escalation_policy.incident_escalation_path.team_escalation
}
