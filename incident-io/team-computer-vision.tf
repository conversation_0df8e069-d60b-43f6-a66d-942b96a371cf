
module "computer_vision_schedule" {
  source = "./modules/incident-schedule"

  team_name = "Computer Vision"
  schedule_versions = [
    {
      effective_from = "2025-04-21T00:00:00Z"
      users = [
        data.incident_user.nathan.id,
        data.incident_user.oren.id,
        data.incident_user.zac.id
      ]
    }
  ]
}

module "computer_vision_escalation_policy" {
  source = "./modules/incident-escalation-policy"

  team_name          = "Computer Vision"
  schedule_id        = module.computer_vision_schedule.schedule_id
  escalation_user_id = data.incident_user.michael.id
  slack_channel_id   = "C088J8G5NPJ" # bot-ops-computer-vision-prod
  working_hours = [
    {
      id       = "default"
      name     = "Working Hours"
      timezone = "America/New_York"
      weekday_intervals = [
        {
          end_time   = "20:00"
          start_time = "09:00"
          weekday    = "monday"
        },
        {
          end_time   = "20:00"
          start_time = "09:00"
          weekday    = "tuesday"
        },
        {
          end_time   = "20:00"
          start_time = "09:00"
          weekday    = "wednesday"
        },
        {
          end_time   = "20:00"
          start_time = "09:00"
          weekday    = "thursday"
        },
        {
          end_time   = "20:00"
          start_time = "09:00"
          weekday    = "friday"
        },
      ]
    },
  ]
}

module "computer_vision_ops_team" {
  source = "./modules/incident-team"

  team_name            = "Computer Vision"
  escalation_policy_id = module.computer_vision_escalation_policy.escalation_policy_id
}

# Moved blocks to prevent resource changes
moved {
  from = module.computer_vision_ops_team.incident_schedule.team_schedule[0]
  to   = module.computer_vision_schedule.incident_schedule.team_schedule
}

moved {
  from = module.computer_vision_ops_team.incident_escalation_path.team_escalation[0]
  to   = module.computer_vision_escalation_policy.incident_escalation_path.team_escalation
}
