# All incident.io user data sources centralized in one file
# These users are referenced across various team configurations

# Clinical Data Team Users
data "incident_user" "celeste" {
  email = "<EMAIL>"
}

data "incident_user" "mike" {
  email = "<EMAIL>"
}

data "incident_user" "ziad" {
  email = "<EMAIL>"
}

data "incident_user" "maurice" {
  email = "<EMAIL>"
}

# Computer Vision Team Users
data "incident_user" "nathan" {
  email = "<EMAIL>"
}

data "incident_user" "oren" {
  email = "<EMAIL>"
}

data "incident_user" "zac" {
  email = "<EMAIL>"
}

data "incident_user" "michael" {
  email = "<EMAIL>"
}

# Data Platform Team Users
data "incident_user" "kamilla" {
  email = "<EMAIL>"
}

data "incident_user" "rob" {
  email = "<EMAIL>"
}

# Field Engineering Team Users
data "incident_user" "nathaniel" {
  email = "<EMAIL>"
}

data "incident_user" "noah" {
  email = "<EMAIL>"
}

data "incident_user" "leo" {
  email = "<EMAIL>"
}

# Forecasting Team Users
data "incident_user" "sean" {
  email = "<EMAIL>"
}

data "incident_user" "alex" {
  email = "<EMAIL>"
}

# Orca Orion Team Users
data "incident_user" "chase" {
  email = "<EMAIL>"
}

data "incident_user" "luke" {
  email = "<EMAIL>"
}

data "incident_user" "ami" {
  email = "<EMAIL>"
}

data "incident_user" "andrea" {
  email = "<EMAIL>"
}

data "incident_user" "anna" {
  email = "<EMAIL>"
}

data "incident_user" "darren" {
  email = "<EMAIL>"
}

# Platform Services Team Users
data "incident_user" "abhay" {
  email = "<EMAIL>"
}

data "incident_user" "christopher" {
  email = "<EMAIL>"
}

data "incident_user" "james" {
  email = "<EMAIL>"
}

data "incident_user" "dorian" {
  email = "<EMAIL>"
}

# Realtime Team Users
data "incident_user" "jesse" {
  email = "<EMAIL>"
}

data "incident_user" "na" {
  email = "<EMAIL>"
}

data "incident_user" "sam" {
  email = "<EMAIL>"
}

data "incident_user" "juandiego" {
  email = "<EMAIL>"
}
