variable "team_name" {
  description = "The name of the team."
  type        = string
}

variable "schedule_id" {
  description = "The ID of the schedule to use in the escalation policy."
  type        = string
}

variable "escalation_user_id" {
  description = "The user ID to escalate to if no team member acknowledges the alert."
  type        = string
}

variable "working_hours" {
  description = "Configuration for the team's working hours, including time zones and weekday intervals."
  type = list(object({
    id       = string
    name     = string
    timezone = string
    weekday_intervals = list(object({
      end_time   = string
      start_time = string
      weekday    = string
    }))
  }))
  default = [
    {
      id       = "default"
      name     = "Working Hours"
      timezone = "America/New_York"
      weekday_intervals = [
        {
          end_time   = "21:00"
          start_time = "07:00"
          weekday    = "monday"
        },
        {
          end_time   = "21:00"
          start_time = "07:00"
          weekday    = "tuesday"
        },
        {
          end_time   = "21:00"
          start_time = "07:00"
          weekday    = "wednesday"
        },
        {
          end_time   = "21:00"
          start_time = "07:00"
          weekday    = "thursday"
        },
        {
          end_time   = "21:00"
          start_time = "07:00"
          weekday    = "friday"
        },
      ]
    },
  ]
}

variable "time_to_ack_seconds" {
  description = "The time (in seconds) allowed for acknowledging an alert."
  type        = number
  default     = 900
}

variable "slack_channel_id" {
  description = "The Slack channel ID where alert notifications will be sent."
  type        = string
}
