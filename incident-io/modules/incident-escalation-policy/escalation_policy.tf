resource "incident_escalation_path" "team_escalation" {
  name          = "${var.team_name} Team Escalation"
  working_hours = var.working_hours

  lifecycle {
    ignore_changes = [
      # To avoid circular references, link teams to escalation paths on the team resource.
      team_ids
    ]
  }

  path = [
    local.slack_alert,
    {
      id   = local.start_node_id
      type = "if_else"
      if_else = {
        # Defined by working_hours property
        conditions = local.is_during_default_primary_working_hours
        then_path  = local.paging_escalation
        else_path = [
          {
            type = "if_else"
            if_else = {
              conditions = local.is_p1_alert_condition
              then_path  = local.paging_escalation
              else_path  = local.slack_alert_wait_until_working_hours
            }
          }
        ]
      }
    }
  ]
}

###### Helpers #######

locals {
  # A random string that must be unique within the escalation path
  start_node_id = "start"
  schedule_id   = var.schedule_id
  # Progressively escalate if unacknowledged:
  # 1. Page the on-call person
  # 2. Page the entire team
  # 3. Page the escalation user (oftentimes the manager)
  paging_escalation = [
    {
      type = "level"
      level = {
        targets = [
          {
            type          = "schedule"
            id            = local.schedule_id
            urgency       = "high"
            schedule_mode = "currently_on_call"
          }
        ]
        time_to_ack_seconds = var.time_to_ack_seconds
      }
    },
    {
      type = "level"
      level = {
        targets = [
          {
            type          = "schedule"
            id            = local.schedule_id
            urgency       = "high"
            schedule_mode = "all_users"
          }
        ]
        time_to_ack_seconds = var.time_to_ack_seconds
      }
    },
    {
      type = "level"
      level = {
        targets = [
          {
            type    = "user"
            id      = var.escalation_user_id
            urgency = "high"
          }
        ]
        time_to_ack_seconds = var.time_to_ack_seconds
      }
    },
    {
      type = "repeat"
      repeat = {
        repeat_times = 1
        to_node      = local.start_node_id
      }
    }
  ]

  slack_alert = {
    # Need to explicitly define an `id` field for the first node in the escalation tree
    id   = "slack-alert"
    type = "notify_channel"
    notify_channel = {
      targets = [
        {
          type    = "slack_channel"
          id      = var.slack_channel_id
          urgency = "high"
        }
      ]
      time_to_ack_seconds = 0
    }
  }


  slack_alert_wait_until_working_hours = [
    {
      type = "notify_channel"
      notify_channel = {
        targets = [
          {
            type    = "slack_channel"
            id      = var.slack_channel_id
            urgency = "high"
          }
        ]
        time_to_ack_weekday_interval_config_id = "default"
        time_to_ack_interval_condition         = "active"
      }
    },
    {
      type = "repeat"
      repeat = {
        repeat_times = 1
        to_node      = local.start_node_id
      }
    }
  ]

  is_p1_alert_condition = [
    {
      operation = "one_of"
      subject   = "escalation.priority"
      param_bindings = [
        {
          array_value = [
            {
              literal = data.incident_catalog_entry.alert_priority_p1.id
            },
          ]
        },
      ]
    },
  ]

  is_during_default_primary_working_hours = [
    {
      operation      = "is_active"
      subject        = "escalation.working_hours[\"${var.working_hours[0].id}\"]"
      param_bindings = []
    },
  ]
}

data "incident_catalog_type" "alert_priority" {
  name = "Alert Priority"
}

data "incident_catalog_entry" "alert_priority_p1" {
  catalog_type_id = data.incident_catalog_type.alert_priority.id
  identifier      = "01JRE0MRTE4NJF3HSSPTN13K6V"
}
