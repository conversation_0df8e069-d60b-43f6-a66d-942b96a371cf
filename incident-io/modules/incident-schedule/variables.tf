variable "team_name" {
  description = "The name of the team."
  type        = string
}

variable "schedule_versions" {
  description = "A list of schedule versions with their effective dates and user assignments."
  type = list(object({
    effective_from = string
    users          = list(string)
  }))
  validation {
    condition     = length(var.schedule_versions) > 0
    error_message = "At least one schedule version is required"
  }
}
