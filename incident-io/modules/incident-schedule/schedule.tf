resource "incident_schedule" "team_schedule" {
  name     = "${var.team_name} Team"
  timezone = "America/New_York"
  holidays_public_config = {
    country_codes = ["US"]
  }

  rotations = [
    {
      id   = "team-${lower(replace(var.team_name, " ", "-"))}-schedule"
      name = "Rotation"
      versions = [
        for version in var.schedule_versions : {
          effective_from    = version.effective_from
          handover_start_at = version.effective_from
          users             = version.users

          layers = [
            {
              name = "Primary"
              id   = "team-${lower(replace(var.team_name, " ", "-"))}-primary"
            },
          ]

          handovers = [
            {
              interval_type = "weekly"
              interval      = 1
            },
          ]
        }
      ]
    },
  ]
}
