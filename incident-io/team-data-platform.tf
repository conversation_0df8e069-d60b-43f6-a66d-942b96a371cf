data "incident_user" "kamilla" {
  email = "<EMAIL>"
}

data "incident_user" "rob" {
  email = "<EMAIL>"
}


module "data_platform_schedule" {
  source = "./modules/incident-schedule"

  team_name = "Data Platform"
  schedule_versions = [
    {
      effective_from = "2025-04-21T00:00:00Z"
      users = [
        data.incident_user.rob.id,
        data.incident_user.kamilla.id,
      ]
    }
  ]
}

module "data_platform_escalation_policy" {
  source = "./modules/incident-escalation-policy"

  team_name          = "Data Platform"
  schedule_id        = module.data_platform_schedule.schedule_id
  escalation_user_id = data.incident_user.michael.id
  slack_channel_id   = "C088851CU8H" # bot-ops-data-platform-prod
}

module "data_platform_ops_team" {
  source = "./modules/incident-team"

  team_name            = "Data Platform"
  escalation_policy_id = module.data_platform_escalation_policy.escalation_policy_id
}

# Moved blocks to prevent resource changes
moved {
  from = module.data_platform_ops_team.incident_schedule.team_schedule[0]
  to   = module.data_platform_schedule.incident_schedule.team_schedule
}

moved {
  from = module.data_platform_ops_team.incident_escalation_path.team_escalation[0]
  to   = module.data_platform_escalation_policy.incident_escalation_path.team_escalation
}
