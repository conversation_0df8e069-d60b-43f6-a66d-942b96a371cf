data "incident_user" "celeste" {
  email = "<EMAIL>"
}

data "incident_user" "mike" {
  email = "<EMAIL>"
}

data "incident_user" "ziad" {
  email = "<EMAIL>"
}

data "incident_user" "maurice" {
  email = "<EMAIL>"
}

module "clinical_data_schedule" {
  source = "./modules/incident-schedule"

  team_name = "Clinical Data"
  schedule_versions = [
    {
      effective_from = "2025-04-21T00:00:00Z"
      users = [
        data.incident_user.celeste.id,
        data.incident_user.mike.id,
        data.incident_user.ziad.id,
        data.incident_user.maurice.id,
      ]
    }
  ]
}

module "clinical_data_escalation_policy" {
  source = "./modules/incident-escalation-policy"

  team_name          = "Clinical Data"
  schedule_id        = module.clinical_data_schedule.schedule_id
  escalation_user_id = data.incident_user.dorian.id
  slack_channel_id   = "C05EGHNTMSS" # team-ehr-interfaces-alert-prod
}

module "clinical_data_team" {
  source = "./modules/incident-team"

  team_name            = "Clinical Data"
  escalation_policy_id = module.clinical_data_escalation_policy.escalation_policy_id
}

# Moved blocks to prevent resource changes
moved {
  from = module.clinical_data_team.incident_schedule.team_schedule[0]
  to   = module.clinical_data_schedule.incident_schedule.team_schedule
}

moved {
  from = module.clinical_data_team.incident_escalation_path.team_escalation[0]
  to   = module.clinical_data_escalation_policy.incident_escalation_path.team_escalation
}
