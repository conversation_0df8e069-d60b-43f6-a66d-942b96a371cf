
module "platform_services_schedule" {
  source = "./modules/incident-schedule"

  team_name = "Platform Services"
  schedule_versions = [
    {
      effective_from = "2025-04-21T00:00:00Z"
      users = [
        data.incident_user.james.id,
        data.incident_user.christopher.id,
        data.incident_user.abhay.id,
      ]
    }
  ]
}

module "platform_services_escalation_policy" {
  source = "./modules/incident-escalation-policy"

  team_name          = "Platform Services"
  schedule_id        = module.platform_services_schedule.schedule_id
  escalation_user_id = data.incident_user.dorian.id
  slack_channel_id   = "C03TU8EFGDT" # team-platform-services-alert-prod
}

module "platform_services_team" {
  source = "./modules/incident-team"

  team_name            = "Platform Services"
  escalation_policy_id = module.platform_services_escalation_policy.escalation_policy_id
}

# Moved blocks to prevent resource changes
moved {
  from = module.platform_services_team.incident_schedule.team_schedule[0]
  to   = module.platform_services_schedule.incident_schedule.team_schedule
}

moved {
  from = module.platform_services_team.incident_escalation_path.team_escalation[0]
  to   = module.platform_services_escalation_policy.incident_escalation_path.team_escalation
}
