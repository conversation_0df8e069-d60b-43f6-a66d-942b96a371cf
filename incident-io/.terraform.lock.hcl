# This file is maintained automatically by "terraform init".
# Manual edits may be lost in future updates.

provider "registry.terraform.io/incident-io/incident" {
  version     = "5.4.1"
  constraints = "~> 5.4.1"
  hashes = [
    "h1:XfQ7VslbHyy3V5B4R+F5omDFBW4Jm71bQ5wCF5OX/MI=",
    "h1:a5zeRmdyGJ0RdtiHZQiWuU/ePTxSQXie2J9zW05TzNc=",
    "zh:03d35217c506a4fa89270907027b84234bd2cfe0f043e40dcc27b1e58219dc6d",
    "zh:1310f4b82f635301b8b7b789968a9ca4cc00ab5038e524181bb628b53141ce66",
    "zh:29fd507a9f7d4a8593582b3bcea64512477a3ead68c9bf861e4f245ba4b77885",
    "zh:3f2685926eeb1e35c259a80f923fcfd24e8d34e84dbf7706144eb93ca44d7c8b",
    "zh:511e77395a2ee4377a1070e2b2f9f540fedd12cfb38e895af0d94d6c23b866eb",
    "zh:6779afbd0cc8c9dcd4536686d2229075a11db67071e3216c27ddd71f01b20f56",
    "zh:75315c08ae29db57c6df1da6a8d56580054e15280914954134bffbaa49ec0b19",
    "zh:85e2100131ab4410438b7ed6fe19419bfd3a66b29cc1e8ea411992b2023f2cb4",
    "zh:9b90e2f3196d8b0d77ac833be6b41ad31fcb2f3ea61d870a0f4298afa373d104",
    "zh:ac61edfe689547d2a3ef5ece6404e83a08d37b52f376dd7fcf7ee21a447e6eae",
    "zh:ad3a3745aeb4caa9c704544bbf47096e7c8805a248b326a94ecd54992cdb4bec",
    "zh:f3437d2aa8dccf1f5626a24a2330975014188d9fe3320d4283dc88c738859241",
    "zh:f4cc1a23869c4b68e1bb8a7c4c1520d6a31d34ce0ac861851d9e8ec0e8d3d8fc",
    "zh:f809ab383cca0a5f83072981c64208cbd7fa67e986a86ee02dd2c82333221e32",
    "zh:fecf8f3cedaa3bdb5e6d5c0b60bd1fb61f47383005c1c458208dc016bb2957c0",
  ]
}
