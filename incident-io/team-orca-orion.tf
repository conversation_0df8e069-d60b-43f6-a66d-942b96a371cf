
module "orca_orion_schedule" {
  source = "./modules/incident-schedule"

  team_name = "Orca Orion"
  schedule_versions = [
    {
      effective_from = "2025-04-21T00:00:00Z"
      users = [
        data.incident_user.darren.id,
        data.incident_user.chase.id,
        data.incident_user.ami.id,
        data.incident_user.luke.id,
        data.incident_user.andrea.id,
        data.incident_user.anna.id,
      ]
    }
  ]
}

module "orca_orion_escalation_policy" {
  source = "./modules/incident-escalation-policy"

  team_name          = "Orca Orion"
  schedule_id        = module.orca_orion_schedule.schedule_id
  escalation_user_id = data.incident_user.juandiego.id
  slack_channel_id   = "C03SK2WLHF1" # team-observations-alert-prod
}

module "orca_orion_team" {
  source = "./modules/incident-team"

  team_name            = "Orca Orion"
  escalation_policy_id = module.orca_orion_escalation_policy.escalation_policy_id
}

# Moved blocks to prevent resource changes
moved {
  from = module.orca_orion_team.incident_schedule.team_schedule[0]
  to   = module.orca_orion_schedule.incident_schedule.team_schedule
}

moved {
  from = module.orca_orion_team.incident_escalation_path.team_escalation[0]
  to   = module.orca_orion_escalation_policy.incident_escalation_path.team_escalation
}
