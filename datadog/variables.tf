variable "datadog_api_key" {
  description = "Datadog API key for GCP & Terraform"
  type        = string
  sensitive   = true
}

variable "datadog_app_key" {
  description = "Datadog App key for Terraform"
  type        = string
  sensitive   = true
}

variable "environments" {
  description = "A list of environments with name and priority"
  type = list(object({
    name     = string,
    priority = number,
  }))
}

variable "extra_tags" {
  type        = list(string)
  description = "Extra tags for alerts"
  default     = []
}

variable "priority" {
  type        = number
  description = "Alert priority"
  default     = 4
}
