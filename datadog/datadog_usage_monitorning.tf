resource "datadog_monitor" "ingested_events" {
  name    = "Alert for number of events ingested to Datadog"
  type    = "metric alert"
  message = "A high volume of events have been ingested to Datadog in the past day. @slack-team-platform-services-alert-prod"

  query = "sum(last_1d):sum:datadog.estimated_usage.logs.ingested_events{*}.as_count() > 450000000"

  monitor_thresholds {
    warning  = 400000000
    critical = 450000000
  }

  include_tags   = true
  notify_no_data = true

  tags = concat([
    "team:platform-services"
  ], var.extra_tags)

  priority = var.priority
}
