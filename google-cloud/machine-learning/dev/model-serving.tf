module "model_serving" {
  source                       = "../modules/model-serving"
  gke_project_id               = local.gke_project_id
  inference_models_bucket_name = module.inference_models.name
  k8s_service_accounts = [
    {
      namespace            = "model-static-start-offset",
      service_account_name = "model-static-start-offset"
    },
    {
      namespace            = "model-turnover",
      service_account_name = "model-turnover"
    },
    {
      namespace            = "model-dynamic-case-end",
      service_account_name = "model-dynamic-case-end"
    },
    {
      namespace            = "model-case-slotting",
      service_account_name = "model-case-slotting"
    },
    {
      namespace            = "model-event-model-forecasts",
      service_account_name = "model-event-model-forecasts",
    },
    {
      namespace            = "forecast-combiner",
      service_account_name = "forecast-combiner"
    },
    {
      namespace            = "apella-yolov5",
      service_account_name = "apella-yolov5"
    },
    {
      namespace            = "resnet-embedder",
      service_account_name = "resnet-embedder"
    },
    {
      namespace            = "model-bayesian-case-duration",
      service_account_name = "model-bayesian-case-duration"
    },
    {
      namespace            = "model-bayesian-case-duration",
      service_account_name = "model-bayesian-case-duration-pre-populate"
    },
    {
      namespace            = "model-bayesian-case-duration-schedule",
      service_account_name = "model-bayesian-case-duration-schedule"
    },
  ]
  authorized_network          = data.terraform_remote_state.nonprod_vpc.outputs.network.id
  redis_memory_size_gb        = 1
  shadow_redis_memory_size_gb = null
}
