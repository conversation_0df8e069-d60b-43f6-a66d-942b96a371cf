# SA and permissions for ml services
resource "google_service_account" "ml_services_sa" {
  account_id   = "ml-services"
  display_name = "Service account for ML services"
}

resource "google_storage_bucket_iam_member" "ml_services_membership" {
  bucket = var.inference_models_bucket_name
  role   = "roles/storage.admin"
  member = "serviceAccount:${google_service_account.ml_services_sa.email}"
}

resource "google_service_account_iam_member" "internal_gke_ml_services_gsa_workload_identity" {
  for_each = { for sa in var.k8s_service_accounts : "${sa.namespace}/${sa.service_account_name}" => sa }

  service_account_id = google_service_account.ml_services_sa.name
  role               = "roles/iam.workloadIdentityUser"
  member             = "serviceAccount:${var.gke_project_id}.svc.id.goog[${each.value.namespace}/${each.value.service_account_name}]"
}
